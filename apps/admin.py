from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    Organization,
    Position,
    Team,
    About,
    AboutVideo,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    SendUsMessage,
    ConsultancyForm,
    Hero,
    Experience,
)

# ============================================================================
# ORGANIZATION & TEAM MANAGEMENT
# ============================================================================

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """Organization management with enhanced display and functionality."""

    list_display = ("name", "address", "email", "contact_number", "logo_preview")
    search_fields = ("name", "address", "email", "contact_number")
    list_filter = ("address",)
    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "address", "logo", "description")
        }),
        ("Contact Details", {
            "fields": ("email", "contact_number")
        }),
        ("Legal Information", {
            "fields": ("pan_no", "reg_no"),
            "classes": ("collapse",)
        }),
    )

    def logo_preview(self, obj):
        """Display logo thumbnail in list view."""
        if obj.logo:
            return format_html(
                '<img src="{}" width="50" height="50" style="border-radius: 5px;" />',
                obj.logo.url
            )
        return "No Logo"
    logo_preview.short_description = "Logo"


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    """Position management for team members."""

    list_display = ("name", "team_count")
    search_fields = ("name",)
    ordering = ("name",)

    def team_count(self, obj):
        """Show number of team members in this position."""
        count = obj.team_set.count()
        if count > 0:
            url = reverse("admin:apps_team_changelist") + f"?position__id__exact={obj.id}"
            return format_html('<a href="{}">{} members</a>', url, count)
        return "0 members"
    team_count.short_description = "Team Members"


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    """Team member management with enhanced display."""

    list_display = ("full_name", "position", "experience", "image_preview", "uploaded_at")
    search_fields = ("first_name", "last_name", "position__name", "experience")
    list_filter = ("position", "uploaded_at")
    ordering = ("position", "first_name")

    fieldsets = (
        ("Personal Information", {
            "fields": ("first_name", "middle_name", "last_name", "image")
        }),
        ("Professional Details", {
            "fields": ("position", "experience")
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )
    readonly_fields = ("uploaded_at",)

    def full_name(self, obj):
        """Display full name of team member."""
        middle = f" {obj.middle_name}" if obj.middle_name else ""
        return f"{obj.first_name}{middle} {obj.last_name}"
    full_name.short_description = "Full Name"

    def image_preview(self, obj):
        """Display team member image thumbnail."""
        if obj.image:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 50%;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Photo"

# ============================================================================
# CONTENT MANAGEMENT (HERO, ABOUT, EXPERIENCE)
# ============================================================================

class AboutVideoInline(admin.TabularInline):
    """Inline admin for About videos with better organization."""

    model = AboutVideo
    extra = 1
    fields = ("video", "order", "video_preview")
    readonly_fields = ("video_preview",)
    ordering = ("order",)

    def video_preview(self, obj):
        """Display video preview if available."""
        if obj.video:
            return format_html(
                '<video width="100" height="60" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "No Video"
    video_preview.short_description = "Preview"


@admin.register(About)
class AboutAdmin(admin.ModelAdmin):
    """About section management with inline videos."""

    inlines = [AboutVideoInline]
    list_display = ("__str__", "image_preview", "video_count", "uploaded_at")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Content", {
            "fields": ("story", "image")
        }),
        ("Mission & Vision", {
            "fields": ("mission", "vision"),
            "description": "Add mission and vision points as JSON array"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        """Display about image thumbnail."""
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Image"

    def video_count(self, obj):
        """Show number of videos associated."""
        count = obj.videos.count()
        return f"{count} video{'s' if count != 1 else ''}"
    video_count.short_description = "Videos"


@admin.register(Hero)
class HeroAdmin(admin.ModelAdmin):
    """Hero section management for homepage."""

    list_display = ("title", "media_preview", "uploaded_at")
    search_fields = ("title", "description")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Content", {
            "fields": ("title", "description")
        }),
        ("Media", {
            "fields": ("image", "video"),
            "description": "Add either image or video for the hero section"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def media_preview(self, obj):
        """Display media preview (image or video)."""
        if obj.image:
            return format_html(
                '<img src="{}" width="80" height="50" style="border-radius: 5px;" />',
                obj.image.url
            )
        elif obj.video:
            return format_html(
                '<video width="80" height="50" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "No Media"
    media_preview.short_description = "Media"


@admin.register(Experience)
class ExperienceAdmin(admin.ModelAdmin):
    """Experience section management."""

    list_display = ("title", "image_preview", "experience_count", "uploaded_at")
    search_fields = ("title", "about")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Content", {
            "fields": ("title", "about", "image")
        }),
        ("Experience Points", {
            "fields": ("experience",),
            "description": "Add experience points as JSON array"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        """Display experience image thumbnail."""
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Image"

    def experience_count(self, obj):
        """Show number of experience points."""
        if obj.experience:
            count = len(obj.experience)
            return f"{count} point{'s' if count != 1 else ''}"
        return "0 points"
    experience_count.short_description = "Experience Points"


@admin.register(Popup)
class PopupAdmin(admin.ModelAdmin):
    """Popup management for promotional content."""

    list_display = ("id", "media_type", "media_preview", "uploaded_at")
    readonly_fields = ("uploaded_at",)

    fieldsets = (
        ("Media Content", {
            "fields": ("image", "video"),
            "description": "Add either image or video for the popup"
        }),
        ("Metadata", {
            "fields": ("uploaded_at",),
            "classes": ("collapse",)
        }),
    )

    def media_type(self, obj):
        """Show what type of media is used."""
        if obj.image and obj.video:
            return "Image & Video"
        elif obj.image:
            return "Image"
        elif obj.video:
            return "Video"
        return "No Media"
    media_type.short_description = "Media Type"

    def media_preview(self, obj):
        """Display media preview."""
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        elif obj.video:
            return format_html(
                '<video width="60" height="40" controls><source src="{}" type="video/mp4"></video>',
                obj.video.url
            )
        return "No Media"
    media_preview.short_description = "Preview"

# ============================================================================
# EDUCATIONAL SERVICES & DESTINATIONS
# ============================================================================

@admin.register(Universities)
class UniversitiesAdmin(admin.ModelAdmin):
    """University management with enhanced display."""

    list_display = ("name", "ranking", "image_preview", "programs_count", "admission_count")
    search_fields = ("name", "ranking", "about")
    list_filter = ("ranking",)
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "image", "ranking", "about")
        }),
        ("Programs & Requirements", {
            "fields": ("program_offered", "admission_required"),
            "description": "Add programs and admission requirements as JSON arrays"
        }),
        ("Additional Details", {
            "fields": ("scholarship", "cost", "duration", "intake"),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        """Display university image thumbnail."""
        if obj.image:
            return format_html(
                '<img src="{}" width="50" height="50" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Logo"

    def programs_count(self, obj):
        """Show number of programs offered."""
        if obj.program_offered:
            count = len(obj.program_offered)
            return f"{count} program{'s' if count != 1 else ''}"
        return "0 programs"
    programs_count.short_description = "Programs"

    def admission_count(self, obj):
        """Show number of admission requirements."""
        if obj.admission_required:
            count = len(obj.admission_required)
            return f"{count} requirement{'s' if count != 1 else ''}"
        return "0 requirements"
    admission_count.short_description = "Requirements"


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    """Study destination management with comprehensive display."""

    list_display = ("name", "flag", "programs", "uni_count", "image_preview")
    search_fields = ("name", "programs", "uni", "description")
    list_filter = ("flag",)
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "image", "flag", "description")
        }),
        ("Academic Details", {
            "fields": ("programs", "uni")
        }),
        ("Study Benefits", {
            "fields": ("why_study", "top_universities", "popular_courses"),
            "description": "Add study benefits, universities, and courses as JSON arrays"
        }),
        ("Requirements & Costs", {
            "fields": ("admission_requirements", "living_cost", "tuition_fees"),
            "classes": ("collapse",)
        }),
        ("Additional Information", {
            "fields": ("scholarship_info", "work_opportunities", "visa_info"),
            "classes": ("collapse",)
        }),
    )

    def image_preview(self, obj):
        """Display destination image thumbnail."""
        if obj.image:
            return format_html(
                '<img src="{}" width="60" height="40" style="border-radius: 5px;" />',
                obj.image.url
            )
        return "No Image"
    image_preview.short_description = "Image"

    def uni_count(self, obj):
        """Show university count information."""
        return obj.uni if obj.uni else "Not specified"
    uni_count.short_description = "Universities"


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """Service management with detailed display."""

    list_display = ("name", "description_preview", "offers_count", "process_count")
    search_fields = ("name", "description")
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "svg", "description")
        }),
        ("Service Benefits", {
            "fields": ("why_to_choose",),
            "description": "Add reasons to choose this service as JSON array"
        }),
        ("Service Offerings", {
            "fields": ("offer",),
            "description": "Add service offerings with service name and summary"
        }),
        ("Process Steps", {
            "fields": ("process",),
            "description": "Add process steps with process name and summary"
        }),
    )

    def description_preview(self, obj):
        """Show truncated description."""
        if len(obj.description) > 50:
            return f"{obj.description[:50]}..."
        return obj.description
    description_preview.short_description = "Description"

    def offers_count(self, obj):
        """Show number of service offers."""
        if obj.offer:
            count = len(obj.offer)
            return f"{count} offer{'s' if count != 1 else ''}"
        return "0 offers"
    offers_count.short_description = "Offers"

    def process_count(self, obj):
        """Show number of process steps."""
        if obj.process:
            count = len(obj.process)
            return f"{count} step{'s' if count != 1 else ''}"
        return "0 steps"
    process_count.short_description = "Process Steps"


@admin.register(TestPreparation)
class TestPreparationAdmin(admin.ModelAdmin):
    """Test preparation management with module details."""

    list_display = ("name", "description_preview", "modules_count", "process_count")
    search_fields = ("name", "description", "about")
    ordering = ("name",)

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "description", "about")
        }),
        ("Course Modules", {
            "fields": ("modules",),
            "description": "Add course modules with name, summary, duration, and sections"
        }),
        ("Preparation Process", {
            "fields": ("process",),
            "description": "Add preparation process steps with name and summary"
        }),
        ("Additional Details", {
            "fields": ("eligibility", "fees", "duration_months"),
            "classes": ("collapse",)
        }),
    )

    def description_preview(self, obj):
        """Show truncated description."""
        if len(obj.description) > 50:
            return f"{obj.description[:50]}..."
        return obj.description
    description_preview.short_description = "Description"

    def modules_count(self, obj):
        """Show number of course modules."""
        if obj.modules:
            count = len(obj.modules)
            total_duration = sum(module.get('duration', 0) for module in obj.modules)
            return f"{count} modules ({total_duration} days)"
        return "0 modules"
    modules_count.short_description = "Modules"

    def process_count(self, obj):
        """Show number of process steps."""
        if obj.process:
            count = len(obj.process)
            return f"{count} step{'s' if count != 1 else ''}"
        return "0 steps"
    process_count.short_description = "Process Steps"

# ============================================================================
# TESTIMONIALS & FEEDBACK
# ============================================================================

@admin.register(Testomonial)
class TestomonialAdmin(admin.ModelAdmin):
    """Testimonial management with rating display."""

    list_display = ("name", "uni", "location", "rating_display", "message_preview", "image_preview")
    search_fields = ("name", "uni", "location", "message")
    list_filter = ("rating", "uni", "location")
    ordering = ("-rating", "name")

    fieldsets = (
        ("Student Information", {
            "fields": ("name", "img", "uni", "location")
        }),
        ("Testimonial", {
            "fields": ("rating", "message")
        }),
    )

    def rating_display(self, obj):
        """Display rating with stars."""
        rating_map = {"one": 1, "two": 2, "three": 3, "four": 4, "five": 5}
        stars = "⭐" * rating_map.get(obj.rating, 0)
        return f"{stars} ({obj.rating})"
    rating_display.short_description = "Rating"

    def message_preview(self, obj):
        """Show truncated message."""
        if len(obj.message) > 60:
            return f"{obj.message[:60]}..."
        return obj.message
    message_preview.short_description = "Message"

    def image_preview(self, obj):
        """Display student image thumbnail."""
        if obj.img:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 50%;" />',
                obj.img.url
            )
        return "No Image"
    image_preview.short_description = "Photo"

# ============================================================================
# FORMS & INQUIRIES
# ============================================================================

@admin.register(SendUsMessage)
class SendUsMessageAdmin(admin.ModelAdmin):
    """Message management with service tracking."""

    list_display = ("full_name", "email", "contact_number", "service", "message_preview", "created_at")
    search_fields = ("full_name", "email", "contact_number", "message")
    list_filter = ("service", "created_at")
    readonly_fields = ("created_at",)
    date_hierarchy = "created_at"
    ordering = ("-created_at",)

    fieldsets = (
        ("Contact Information", {
            "fields": ("full_name", "email", "contact_number")
        }),
        ("Inquiry Details", {
            "fields": ("service", "message")
        }),
        ("Metadata", {
            "fields": ("created_at",),
            "classes": ("collapse",)
        }),
    )

    def message_preview(self, obj):
        """Show truncated message."""
        if len(obj.message) > 50:
            return f"{obj.message[:50]}..."
        return obj.message
    message_preview.short_description = "Message"

    actions = ["mark_as_processed"]

    def mark_as_processed(self, request, queryset):
        """Custom action to mark messages as processed."""
        # This would require adding a processed field to the model
        # For now, it's a placeholder for future enhancement
        self.message_user(request, f"Selected {queryset.count()} messages marked for processing.")
    mark_as_processed.short_description = "Mark selected messages as processed"


@admin.register(ConsultancyForm)
class ConsultancyFormAdmin(admin.ModelAdmin):
    """Consultancy form management with comprehensive filtering."""

    list_display = ("full_name", "email", "contact_number", "education", "country", "test", "message_preview")
    search_fields = ("full_name", "email", "contact_number", "message")
    list_filter = ("education", "country", "test")
    ordering = ("-id",)

    fieldsets = (
        ("Personal Information", {
            "fields": ("full_name", "email", "contact_number")
        }),
        ("Educational Background", {
            "fields": ("education",)
        }),
        ("Study Preferences", {
            "fields": ("country", "test")
        }),
        ("Additional Information", {
            "fields": ("message",)
        }),
    )

    def message_preview(self, obj):
        """Show truncated message."""
        if len(obj.message) > 50:
            return f"{obj.message[:50]}..."
        return obj.message
    message_preview.short_description = "Message"

    actions = ["export_to_csv", "mark_as_contacted"]

    def export_to_csv(self, request, queryset):
        """Custom action to export consultancy forms to CSV."""
        # This would require implementing CSV export functionality
        self.message_user(request, f"Exporting {queryset.count()} consultancy forms to CSV.")
    export_to_csv.short_description = "Export selected forms to CSV"

    def mark_as_contacted(self, request, queryset):
        """Custom action to mark forms as contacted."""
        # This would require adding a contacted field to the model
        self.message_user(request, f"Marked {queryset.count()} forms as contacted.")
    mark_as_contacted.short_description = "Mark selected forms as contacted"

# ============================================================================
# ADMIN SITE CUSTOMIZATION
# ============================================================================

# Customize admin site header and title
admin.site.site_header = "NextGen Education Admin"
admin.site.site_title = "NextGen Admin"
admin.site.index_title = "Welcome to NextGen Education Administration"

# Group models in admin index for better organization
# This can be further enhanced with custom AdminConfig if needed
