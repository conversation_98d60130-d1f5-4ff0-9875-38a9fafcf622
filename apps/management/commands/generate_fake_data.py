from django.core.management.base import BaseCommand
from faker import Faker
from apps.models import (
    Organization,
    Position,
    Team,
    About,
    AboutVideo,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    ConsultancyForm,
    SendUsMessage,
    Hero,
    Experience,
)
import random
import requests
from django.core.files.base import ContentFile
from PIL import Image
import io
import time

fake = Faker()


class Command(BaseCommand):
    help = "Generate fake data for testing"

    def download_image_from_google(self, query, width=800, height=600):
        """Download an image from Google Images using a search query"""
        try:
            # Using Lorem Picsum as a reliable alternative to Google Images
            # This provides random images with specified dimensions
            url = f"https://picsum.photos/{width}/{height}"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                # Create a ContentFile from the image data
                image_content = ContentFile(response.content)
                # Generate a filename based on the query
                filename = f"{query.replace(' ', '_').lower()}_{fake.uuid4()[:8]}.jpg"
                image_content.name = filename
                return image_content
            else:
                self.stdout.write(f"Failed to download image for {query}")
                return None
        except Exception as e:
            self.stdout.write(f"Error downloading image for {query}: {str(e)}")
            return None

    def create_placeholder_image(self, width=800, height=600, color=(200, 200, 200)):
        """Create a placeholder image if download fails"""
        try:
            # Create a simple colored rectangle as placeholder
            img = Image.new('RGB', (width, height), color)
            img_io = io.BytesIO()
            img.save(img_io, format='JPEG', quality=85)
            img_io.seek(0)

            image_content = ContentFile(img_io.getvalue())
            image_content.name = f"placeholder_{fake.uuid4()[:8]}.jpg"
            return image_content
        except Exception as e:
            self.stdout.write(f"Error creating placeholder image: {str(e)}")
            return None

    def handle(self, *args, **kwargs):
        try:
            self.stdout.write("Generating fake data...")

            # Generate Positions
            positions = []
            position_titles = ["CEO", "Manager", "Consultant", "Advisor", "Coordinator"]
            for title in position_titles:
                position = Position.objects.create(name=title)
                positions.append(position)
            self.stdout.write("✓ Generated positions")

            # Generate Organization
            org_logo = self.download_image_from_google("company logo business", 400, 400)
            if not org_logo:
                org_logo = self.create_placeholder_image(400, 400, (100, 150, 200))

            Organization.objects.create(
                name=fake.company(),
                address=fake.address(),
                logo=org_logo,
                contact_number=fake.numerify("##########"),
                email=fake.email(),
                pan_no=fake.numerify("ABCDE####F"),
                reg_no=fake.numerify("REG#######"),
                description=fake.text(),
            )
            self.stdout.write("✓ Generated organization")

            # Generate Team members
            for i in range(5):
                team_image = self.download_image_from_google("professional headshot business person", 300, 400)
                if not team_image:
                    team_image = self.create_placeholder_image(300, 400, (150, 100, 200))

                Team.objects.create(
                    first_name=fake.first_name(),
                    middle_name=fake.first_name(),
                    last_name=fake.last_name(),
                    image=team_image,
                    position=random.choice(positions),
                    experience=f"{random.randint(1, 20)} years",
                )
                time.sleep(0.5)  # Small delay to avoid rate limiting
            self.stdout.write("✓ Generated team members")

            # Generate About
            about_image = self.download_image_from_google("office building company", 800, 600)
            if not about_image:
                about_image = self.create_placeholder_image(800, 600, (120, 180, 150))

            # Generate mission and vision as JSON arrays
            mission_points = [fake.sentence() for _ in range(3)]
            vision_points = [fake.sentence() for _ in range(3)]

            About.objects.create(
                story=fake.text(max_nb_chars=1000),
                mission=mission_points,
                vision=vision_points,
                image=about_image,
            )
            self.stdout.write("✓ Generated about section")

            # Generate Universities
            for i in range(5):
                uni_image = self.download_image_from_google("university campus building", 600, 400)
                if not uni_image:
                    uni_image = self.create_placeholder_image(600, 400, (100, 120, 180))

                # Generate JSON data for universities
                programs = [fake.job() + " Studies" for _ in range(3)]
                admission_reqs = ["High School Diploma", "English Proficiency", "Application Essay"]
                scholarships = ["Merit Scholarship", "Need-based Aid", "International Student Grant"]

                cost_data = [{
                    "tuition_fee": f"${random.randint(20000, 60000)}",
                    "cost": f"${random.randint(25000, 70000)}",
                    "living_expense": f"${random.randint(10000, 20000)}",
                    "expense": f"${random.randint(5000, 15000)}"
                }]

                Universities.objects.create(
                    name=fake.company() + " University",
                    image=uni_image,
                    ranking=f"#{random.randint(1, 100)}",
                    about=fake.text(max_nb_chars=1000),
                    program_offered=programs,
                    admission_required=admission_reqs,
                    cost=cost_data,
                    scholarship=scholarships,
                )
                time.sleep(0.5)
            self.stdout.write("✓ Generated universities")

            # Generate Destinations
            destinations = []
            countries = ["USA", "UK", "Canada", "Australia", "Germany"]
            flags = ["🇺🇸", "🇬🇧", "🇨🇦", "🇦🇺", "🇩🇪"]

            for i, country in enumerate(countries):
                dest_image = self.download_image_from_google(f"{country} landmark cityscape", 800, 500)
                if not dest_image:
                    dest_image = self.create_placeholder_image(800, 500, (80, 150, 120))

                # Generate JSON data for destinations
                why_study = [f"High quality education in {country}", f"Great career opportunities", f"Cultural diversity"]
                top_unis = [fake.company() + " University" for _ in range(3)]
                popular_courses = ["Computer Science", "Business Administration", "Engineering"]

                cost_data = [{
                    "tuition_fee": f"${random.randint(15000, 50000)}",
                    "cost": f"${random.randint(20000, 60000)}",
                    "living_expense": f"${random.randint(8000, 18000)}",
                    "expense": f"${random.randint(3000, 12000)}",
                    "total_expense": f"${random.randint(30000, 80000)}",
                    "total_cost": f"${random.randint(35000, 90000)}"
                }]

                destination = Destination.objects.create(
                    name=country,
                    image=dest_image,
                    programs=fake.text(max_nb_chars=200),
                    uni=fake.company() + " University",
                    flag=flags[i],
                    description=fake.text(max_nb_chars=1000),
                    why_study=why_study,
                    top_universities=top_unis,
                    popular_courses=popular_courses,
                    cost_of_study=cost_data,
                )
                destinations.append(destination)
                time.sleep(0.5)
            self.stdout.write("✓ Generated destinations")

            # Generate Services
            services = []
            service_names = [
                "Study Abroad",
                "Visa Assistance",
                "Test Preparation",
                "Career Counseling",
                "Documentation",
            ]

            for name in service_names:
                # Generate JSON data for services
                why_choose = [f"Expert guidance in {name.lower()}", "Proven track record", "Personalized approach"]

                offer_data = [
                    {"service": f"{name} Consultation", "summary": fake.text(max_nb_chars=100)},
                    {"service": f"{name} Support", "summary": fake.text(max_nb_chars=100)},
                    {"service": f"{name} Follow-up", "summary": fake.text(max_nb_chars=100)}
                ]

                process_data = [
                    {"process": "Initial Assessment", "summary": fake.text(max_nb_chars=100)},
                    {"process": "Planning & Strategy", "summary": fake.text(max_nb_chars=100)},
                    {"process": "Implementation", "summary": fake.text(max_nb_chars=100)},
                    {"process": "Follow-up & Support", "summary": fake.text(max_nb_chars=100)}
                ]

                service = Service.objects.create(
                    name=name,
                    svg=f'<svg width="50" height="50"><circle cx="25" cy="25" r="20" fill="blue"/></svg>',
                    description=fake.text(max_nb_chars=200),
                    why_to_choose=why_choose,
                    offer=offer_data,
                    process=process_data,
                )
                services.append(service)
            self.stdout.write("✓ Generated services")

            # Generate Test Preparations
            test_preps = []
            test_names = ["IELTS", "TOEFL", "GRE", "GMAT", "SAT"]

            for name in test_names:
                # Generate modules data
                modules_data = [
                    {
                        "name": f"{name} Reading",
                        "summary": fake.text(max_nb_chars=100),
                        "duration": random.randint(30, 90),
                        "section": random.randint(1, 4)
                    },
                    {
                        "name": f"{name} Writing",
                        "summary": fake.text(max_nb_chars=100),
                        "duration": random.randint(30, 90),
                        "section": random.randint(1, 4)
                    },
                    {
                        "name": f"{name} Speaking",
                        "summary": fake.text(max_nb_chars=100),
                        "duration": random.randint(15, 60),
                        "section": random.randint(1, 4)
                    }
                ]

                # Generate process data
                process_data = [
                    {"name": "Assessment", "summary": fake.text(max_nb_chars=100)},
                    {"name": "Study Plan", "summary": fake.text(max_nb_chars=100)},
                    {"name": "Practice Tests", "summary": fake.text(max_nb_chars=100)},
                    {"name": "Final Preparation", "summary": fake.text(max_nb_chars=100)}
                ]

                test_prep = TestPreparation.objects.create(
                    name=name,
                    description=fake.text(max_nb_chars=200),
                    about=fake.text(max_nb_chars=500),
                    modules=modules_data,
                    process=process_data,
                )
                test_preps.append(test_prep)
            self.stdout.write("✓ Generated test preparations")

            # Generate Testimonials
            ratings = ["one", "two", "three", "four", "five"]
            for i in range(10):
                testimonial_image = self.download_image_from_google("student portrait graduation", 200, 200)
                if not testimonial_image:
                    testimonial_image = self.create_placeholder_image(200, 200, (150, 200, 100))

                Testomonial.objects.create(
                    name=fake.name(),
                    img=testimonial_image,
                    uni=fake.company() + " University",
                    location=fake.city(),
                    rating=random.choice(ratings),
                    message=fake.text(max_nb_chars=200),
                )
                time.sleep(0.3)
            self.stdout.write("✓ Generated testimonials")

            # Generate Consultancy Forms
            education_levels = ["12", "Diploma", "Bachelor", "Masters", "Phd"]
            for _ in range(20):
                ConsultancyForm.objects.create(
                    full_name=fake.name(),
                    contact_number=fake.numerify("##########"),
                    email=fake.email(),
                    education=random.choice(education_levels),
                    country=random.choice(destinations),
                    test=random.choice(test_preps),
                    message=fake.text(max_nb_chars=200),
                )
            self.stdout.write("✓ Generated consultancy forms")

            # Generate Send Us Messages
            for _ in range(15):
                SendUsMessage.objects.create(
                    full_name=fake.name(),
                    email=fake.email(),
                    contact_number=fake.numerify("##########"),
                    service=random.choice(services),
                    message=fake.text(max_nb_chars=200),
                )
            self.stdout.write("✓ Generated send us messages")

            # Generate Hero sections
            for i in range(3):
                hero_image = self.download_image_from_google("education students success", 1200, 600)
                if not hero_image:
                    hero_image = self.create_placeholder_image(1200, 600, (50, 100, 200))

                Hero.objects.create(
                    title=fake.sentence(nb_words=6),
                    description=fake.text(max_nb_chars=500),
                    image=hero_image,
                )
                time.sleep(0.5)
            self.stdout.write("✓ Generated hero sections")

            # Generate Experience sections
            for i in range(2):
                exp_image = self.download_image_from_google("business success achievement", 800, 600)
                if not exp_image:
                    exp_image = self.create_placeholder_image(800, 600, (200, 150, 50))

                experience_points = [fake.sentence() for _ in range(4)]

                Experience.objects.create(
                    title=fake.sentence(nb_words=4),
                    about=fake.text(max_nb_chars=500),
                    experience=experience_points,
                    image=exp_image,
                )
                time.sleep(0.5)
            self.stdout.write("✓ Generated experience sections")

            # Generate Popups
            for i in range(2):
                popup_image = self.download_image_from_google("announcement promotion banner", 600, 400)
                if not popup_image:
                    popup_image = self.create_placeholder_image(600, 400, (255, 200, 100))

                Popup.objects.create(
                    image=popup_image,
                )
                time.sleep(0.5)
            self.stdout.write("✓ Generated popups")

            self.stdout.write(
                self.style.SUCCESS("Successfully generated all fake data!")
            )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error generating fake data: {str(e)}"))
