from django.contrib import admin
from .models import (
    Organization,
    Position,
    Team,
    About,
    AboutVideo,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    SendUsMessage,
    ConsultancyForm,
    Hero,
    Experience,
)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ("name", "address", "email", "contact_number")
    search_fields = ("name", "address", "email")
    list_filter = ("address",)


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Team)
class TeamAdmin(admin.ModelAdmin):
    list_display = ("first_name", "last_name", "position", "experience", "uploaded_at")
    search_fields = ("first_name", "last_name", "position__name", "experience")
    list_filter = ("position", "uploaded_at")


class AboutVideoInline(admin.TabularInline):
    model = AboutVideo
    extra = 1
    fields = ("video", "order")
    ordering = ("order",)


@admin.register(About)
class AboutAdmin(admin.ModelAdmin):
    inlines = [AboutVideoInline]
    list_display = ("__str__", "uploaded_at")
    readonly_fields = ("uploaded_at",)



@admin.register(Popup)
class PopupAdmin(admin.ModelAdmin):
    list_display = ("id", "uploaded_at")
    readonly_fields = ("uploaded_at",)


@admin.register(Universities)
class UniversitiesAdmin(admin.ModelAdmin):
    list_display = ("id", "name", "ranking")
    search_fields = (
        "name",
        "ranking",
    )


@admin.register(Destination)
class DestinationAdmin(admin.ModelAdmin):
    list_display = ("name", "programs", "uni")
    search_fields = ("name", "programs")


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ("name", "description")
    search_fields = ("name",)


@admin.register(TestPreparation)
class TestPreparationAdmin(admin.ModelAdmin):
    list_display = ("name", "description")
    search_fields = ("name",)


@admin.register(Testomonial)
class TestomonialAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "uni",
        "message",
        "rating",
    )
    search_fields = ("name", "rating")


@admin.register(SendUsMessage)
class SendUsMessageAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "email",
        "contact_number",
        "service",
        "message",
        "created_at",
    )
    search_fields = ("full_name", "email", "contact_number")
    list_filter = ("service", "created_at")
    readonly_fields = ("created_at",)


@admin.register(ConsultancyForm)
class ConsultancyFormAdmin(admin.ModelAdmin):
    list_display = (
        "full_name",
        "email",
        "contact_number",
        "education",
        "country",
        "test",
    )
    search_fields = ("full_name", "email", "contact_number", "education")
    list_filter = ("education", "country", "test")


@admin.register(Hero)
class HeroAdmin(admin.ModelAdmin):
    list_display = ("id", "title", "uploaded_at")
    search_fields = ("title", "description")
    readonly_fields = ("uploaded_at",)


@admin.register(Experience)
class ExperienceAdmin(admin.ModelAdmin):
    list_display = ("id", "title", "uploaded_at")
    search_fields = ("title", "about")
    readonly_fields = ("uploaded_at",)
