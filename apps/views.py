from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from .models import (
    Organization,
    Position,
    Team,
    About,
    AboutVideo,
    Popup,
    Universities,
    Destination,
    Service,
    TestPreparation,
    Testomonial,
    SendUsMessage,
    ConsultancyForm,
    Hero,
    Experience,
)
from .serializers import (
    OrganizationSerializer,
    PositionSerializer,
    TeamSerializer,
    AboutSerializer,
    AboutVideoSerializer,
    PopupSerializer,
    UniversitiesSerializer,
    DestinationSerializer,
    ServiceSerializer,
    TestPreparationSerializer,
    TestomonialSerializer,
    SendUsMessageSerializer,
    ConsultancyFormSerializer,
    HeroSerializer,
    ExperienceSerializer,
)
from django.conf import settings
from django.http import HttpResponse, Http404
import os
import mimetypes

# Create your views here.

class OrganizationViewSet(viewsets.ModelViewSet):
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    pagination_class = None


class PositionViewSet(viewsets.ModelViewSet):
    queryset = Position.objects.all()
    serializer_class = PositionSerializer
    pagination_class = None


class TeamViewSet(viewsets.ModelViewSet):
    queryset = Team.objects.all()
    serializer_class = TeamSerializer
    pagination_class = None


class AboutViewSet(viewsets.ModelViewSet):
    queryset = About.objects.all()
    serializer_class = AboutSerializer
    pagination_class = None


class AboutVideoViewSet(viewsets.ModelViewSet):
    queryset = AboutVideo.objects.all()
    serializer_class = AboutVideoSerializer
    pagination_class = None


class PopupViewSet(viewsets.ModelViewSet):
    queryset = Popup.objects.all()
    serializer_class = PopupSerializer
    pagination_class = None


class UniversitiesViewSet(viewsets.ModelViewSet):
    queryset = Universities.objects.all()
    serializer_class = UniversitiesSerializer
    pagination_class = None


class DestinationViewSet(viewsets.ModelViewSet):
    queryset = Destination.objects.all()
    serializer_class = DestinationSerializer
    pagination_class = None


class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    pagination_class = None


class TestPreparationViewSet(viewsets.ModelViewSet):
    queryset = TestPreparation.objects.all()
    serializer_class = TestPreparationSerializer
    pagination_class = None


class TestomonialViewSet(viewsets.ModelViewSet):
    queryset = Testomonial.objects.all()
    serializer_class = TestomonialSerializer
    pagination_class = None


class SendUsMessageViewSet(viewsets.ModelViewSet):
    queryset = SendUsMessage.objects.all()
    serializer_class = SendUsMessageSerializer
    pagination_class = None
    permission_classes = [AllowAny]  # Allow any user to send messages
    ordering = ["-created_at"]


class ConsultancyFormViewSet(viewsets.ModelViewSet):
    queryset = ConsultancyForm.objects.all()
    serializer_class = ConsultancyFormSerializer
    pagination_class = None
    permission_classes = [AllowAny]


class HeroViewSet(viewsets.ModelViewSet):
    queryset = Hero.objects.all()
    serializer_class = HeroSerializer
    pagination_class = None


class ExperienceViewSet(viewsets.ModelViewSet):
    queryset = Experience.objects.all()
    serializer_class = ExperienceSerializer
    pagination_class = None


# Universal media serving view for development


def dev_serve_media(request, path):
    # Only allow in DEBUG mode!
    if not settings.DEBUG:
        raise Http404("Not found")
    media_path = os.path.join(settings.MEDIA_ROOT, path)
    if not os.path.exists(media_path) or not os.path.isfile(media_path):
        raise Http404("Media file not found: " + media_path)
    with open(media_path, "rb") as f:
        data = f.read()
    content_type, _ = mimetypes.guess_type(media_path)
    return HttpResponse(data, content_type=content_type or "application/octet-stream")
