[project]
name = "nextgen-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "asgiref==3.8.1",
    "django==5.2.1",
    "django-ckeditor-5==0.2.18",
    "django-cors-headers==4.7.0",
    "django-filter==25.1",
    "django-jazzmin==3.0.1",
    "django-js-asset==3.1.2",
    "django-jsonform>=2.23.2",
    "django-rest-framework==0.1.0",
    "djangorestframework==3.16.0",
    "drf-yasg==1.21.10",
    "faker>=37.3.0",
    "inflection==0.5.1",
    "packaging==25.0",
    "pillow==11.2.1",
    "psycopg2-binary==2.9.10",
    "pytz==2025.2",
    "pyyaml==6.0.2",
    "requests>=2.32.4",
    "sqlparse==0.5.3",
    "tzdata==2025.2",
    "uritemplate==4.1.1",
    "whitenoise>=6.9.0",
]
